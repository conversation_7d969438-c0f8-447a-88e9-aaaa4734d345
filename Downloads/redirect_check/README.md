# Redirect Checker

This tool is used to check if URL redirects are working as expected.

## Environment Setup

1. Ensure Python 3.6+ is installed
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. Place CSV files in the `csv` directory with the following format:
   - Each row contains two columns: source URL and expected target URL
   - No header row is needed
   - See `csv/sample.csv` for an example

2. Run the script:

```bash
python redirect_check.py
```

3. View results:
   - ✅ indicates correct redirection
   - ⚠️ indicates redirection to a different URL than expected
   - ❌ indicates request failure

   After processing each CSV file, a summary will be displayed showing:
   - Total number of URLs checked
   - Number of successful redirects
   - Number of URLs with different targets than expected
   - Number of failed requests
   - Detailed list of URLs with different targets
   - Detailed list of failed URLs

## Custom Settings

If you need to add cookies (e.g., for websites that require login), add cookie information in the headers section of the `redirect_check.py` file.
