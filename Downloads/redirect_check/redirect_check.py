import httpx
import csv
import os
from pathlib import Path

headers = {
    "User-Agent": "Mozilla/5.0 (Redirect Checker)",
    # If you need to add login cookies, add them here:
    # "Cookie": "your_cookie_here"
}

def check_redirect_httpx(source_url, expected_url):
    try:
        with httpx.Client(follow_redirects=True, timeout=10, verify=False) as client:
            r = client.get(source_url, headers=headers)
            final_url = str(r.url)
            if final_url == expected_url:
                print(f"✅ {source_url} → {final_url}")
                return "success"
            else:
                print(f"⚠️ {source_url} → {final_url} (Expected: {expected_url})")
                return "different", final_url, expected_url
    except Exception as e:
        print(f"❌ {source_url} failed: {e}")
        return "failed", str(e)

def load_redirects_from_csv(csv_path):
    redirect_pairs = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) >= 2:  # Ensure row has at least two columns
                source_url = row[0].strip()
                target_url = row[1].strip()
                redirect_pairs.append((source_url, target_url))
    return redirect_pairs

def print_summary(results):
    success_count = len([r for r in results if r["status"] == "success"])
    different_count = len([r for r in results if r["status"] == "different"])
    failed_count = len([r for r in results if r["status"] == "failed"])

    print("\n" + "="*50)
    print("SUMMARY")
    print("="*50)
    print(f"Total URLs checked: {len(results)}")
    print(f"✅ Successful redirects: {success_count}")
    print(f"⚠️ Different target URLs: {different_count}")
    print(f"❌ Failed requests: {failed_count}")

    if different_count > 0:
        print("\nURLs with different targets:")
        print("-"*50)
        for r in results:
            if r["status"] == "different":
                print(f"Source: {r['source_url']}")
                print(f"  Actual: {r['actual_url']}")
                print(f"  Expected: {r['expected_url']}")
                print()

    if failed_count > 0:
        print("\nFailed URLs:")
        print("-"*50)
        for r in results:
            if r["status"] == "failed":
                print(f"Source: {r['source_url']}")
                print(f"  Error: {r['error']}")
                print()

def main():
    # Create csv directory (if it doesn't exist)
    csv_dir = Path("./csv")
    csv_dir.mkdir(exist_ok=True)

    # Get all csv files in the csv directory
    csv_files = list(csv_dir.glob("*.csv"))

    if not csv_files:
        print("No CSV files found. Please place CSV files in the ./csv/ directory.")
        return

    # Process each CSV file
    for csv_file in csv_files:
        print(f"\nProcessing file: {csv_file}")
        redirect_pairs = load_redirects_from_csv(csv_file)
        print(f"Loaded {len(redirect_pairs)} redirect URL pairs")

        results = []
        for source_url, target_url in redirect_pairs:
            result = check_redirect_httpx(source_url, target_url)

            if result == "success":
                results.append({
                    "source_url": source_url,
                    "status": "success"
                })
            elif result[0] == "different":
                results.append({
                    "source_url": source_url,
                    "status": "different",
                    "actual_url": result[1],
                    "expected_url": result[2]
                })
            elif result[0] == "failed":
                results.append({
                    "source_url": source_url,
                    "status": "failed",
                    "error": result[1]
                })

        # Print summary for this CSV file
        print_summary(results)

if __name__ == "__main__":
    main()
